import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  selectUI, 
  selectCurrentUserDetail,
  showUserDetailModal,
  hideUserDetailModal 
} from '../../redux/slices/adminDashboardSlice';

const UserModalTest = () => {
  const dispatch = useDispatch();
  const ui = useSelector(selectUI);
  const currentUser = useSelector(selectCurrentUserDetail);

  const testUser = {
    _id: 'test123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    role: 'buyer',
    status: 'active',
    createdAt: new Date().toISOString(),
    profileImage: null
  };

  const handleShowModal = () => {
    console.log('Test: Showing modal with user:', testUser);
    dispatch(showUserDetailModal(testUser));
  };

  const handleHideModal = () => {
    console.log('Test: Hiding modal');
    dispatch(hideUserDetailModal());
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>User Modal Test Component</h3>
      <div style={{ marginBottom: '10px' }}>
        <strong>UI State:</strong>
        <pre>{JSON.stringify(ui, null, 2)}</pre>
      </div>
      <div style={{ marginBottom: '10px' }}>
        <strong>Current User:</strong>
        <pre>{JSON.stringify(currentUser, null, 2)}</pre>
      </div>
      <div>
        <button 
          onClick={handleShowModal}
          style={{ 
            padding: '10px 20px', 
            marginRight: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Show Modal
        </button>
        <button 
          onClick={handleHideModal}
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Hide Modal
        </button>
      </div>
    </div>
  );
};

export default UserModalTest;
