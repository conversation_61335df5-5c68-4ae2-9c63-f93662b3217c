import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectUsers,
  selectSelectedUsers,
  selectUI,
  setSelectedUsers,
  showUserDetailModal,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  fetchUsers,
  updateUser,
  deleteUser,
  bulkUpdateUsers,
  bulkDeleteUsers,
} from "../../redux/slices/adminDashboardThunks";
import { showSuccess, showError } from "../../utils/toast";
import AdminLayout from "../../components/admin/AdminLayout";
import UserDetailModal from "../../components/admin/UserDetailModal";
import UserModalTest from "../../components/admin/UserModalTest";
import "../../styles/AdminUserManagement.css";

// Icons
import { FaUsers, FaUserTie, <PERSON>a<PERSON>ear<PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaEdit, Fa<PERSON>rash, <PERSON>a<PERSON>oggleOn, FaToggleOff } from "react-icons/fa";

const AdminUserManagement = () => {
  const dispatch = useDispatch();
  const users = useSelector(selectUsers);
  const selectedUsers = useSelector(selectSelectedUsers);
  const ui = useSelector(selectUI);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");

  // Fetch users on component mount
  useEffect(() => {
    dispatch(fetchUsers());
  }, [dispatch]);



  // Filter users based on search and filters
  const filteredUsers = (users.data || []).filter(user => {
    // Safely access properties with fallbacks
    const firstName = user?.firstName || '';
    const lastName = user?.lastName || '';
    const email = user?.email || '';
    const role = user?.role || '';
    const status = user?.status || '';

    const matchesSearch =
      firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === "all" || role === roleFilter;
    const matchesStatus = statusFilter === "all" || status === statusFilter;

    return matchesSearch && matchesRole && matchesStatus;
  });

  // Handle select all
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      dispatch(setSelectedUsers(filteredUsers.map(user => user.id)));
    } else {
      dispatch(setSelectedUsers([]));
    }
  };

  // Handle individual select
  const handleSelectUser = (userId) => {
    const newSelection = selectedUsers.includes(userId)
      ? selectedUsers.filter(id => id !== userId)
      : [...selectedUsers, userId];
    dispatch(setSelectedUsers(newSelection));
  };

  // Handle user actions
  const handleUserAction = (user, action) => {
    console.log('Action triggered:', action, 'User:', user);
    switch (action) {
      case 'view':
      case 'edit':
        console.log('Dispatching showUserDetailModal');
        dispatch(showUserDetailModal(user));
        console.log('UI state after dispatch:', ui);
        break;
      case 'delete':
        handleDeleteUser(user);
        break;
      case 'toggle':
        handleToggleUserStatus(user);
        break;
      default:
        break;
    }
  };

  // Handle delete user
  const handleDeleteUser = async (user) => {
    if (window.confirm(`Are you sure you want to delete user "${user.firstName} ${user.lastName}"? This action cannot be undone.`)) {
      try {
        await dispatch(deleteUser(user.id || user._id)).unwrap();
        dispatch(addActivity({
          id: Date.now(),
          type: 'user_deletion',
          description: `User deleted: ${user.firstName} ${user.lastName}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));

        // Refresh the users list
        dispatch(fetchUsers());

        showSuccess(`User "${user.firstName} ${user.lastName}" has been deleted successfully!`);
      } catch (error) {
        console.error('Failed to delete user:', error);
        showError(`Failed to delete user: ${error.message || 'Please try again.'}`);
      }
    }
  };

  // Handle toggle user status
  const handleToggleUserStatus = async (user) => {
    const newStatus = user.status === 'active' ? 'inactive' : 'active';
    const actionText = newStatus === 'active' ? 'activate' : 'deactivate';

    if (!window.confirm(`Are you sure you want to ${actionText} this user?`)) {
      return;
    }

    try {
      await dispatch(updateUser({ id: user.id || user._id, userData: { status: newStatus } })).unwrap();
      dispatch(addActivity({
        id: Date.now(),
        type: 'user_status_change',
        description: `User ${newStatus}: ${user.firstName} ${user.lastName}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));

      // Refresh the users list
      dispatch(fetchUsers());

      showSuccess(`User has been ${actionText}d successfully!`);
    } catch (error) {
      console.error('Failed to update user status:', error);
      showError(`Failed to ${actionText} user: ${error.message || 'Please try again.'}`);
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    if (selectedUsers.length === 0) {
      alert('Please select users first');
      return;
    }

    switch (action) {
      case 'activate':
        if (window.confirm(`Activate ${selectedUsers.length} selected users?`)) {
          try {
            await dispatch(bulkUpdateUsers({ userIds: selectedUsers, action: 'activate' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_user_activation',
              description: `Bulk activated ${selectedUsers.length} users`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            dispatch(setSelectedUsers([]));

            // Refresh the users list
            dispatch(fetchUsers());

            showSuccess(`${selectedUsers.length} users have been activated successfully!`);
          } catch (error) {
            console.error('Failed to activate users:', error);
            showError(`Failed to activate users: ${error.message || 'Please try again.'}`);
          }
        }
        break;
      case 'deactivate':
        if (window.confirm(`Deactivate ${selectedUsers.length} selected users?`)) {
          try {
            await dispatch(bulkUpdateUsers({ userIds: selectedUsers, action: 'deactivate' })).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_user_deactivation',
              description: `Bulk deactivated ${selectedUsers.length} users`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            dispatch(setSelectedUsers([]));

            // Refresh the users list
            dispatch(fetchUsers());

            showSuccess(`${selectedUsers.length} users have been deactivated successfully!`);
          } catch (error) {
            console.error('Failed to deactivate users:', error);
            showError(`Failed to deactivate users: ${error.message || 'Please try again.'}`);
          }
        }
        break;
      case 'delete':
        if (window.confirm(`Delete ${selectedUsers.length} selected users? This action cannot be undone.`)) {
          try {
            await dispatch(bulkDeleteUsers(selectedUsers)).unwrap();
            dispatch(addActivity({
              id: Date.now(),
              type: 'bulk_user_deletion',
              description: `Bulk deleted ${selectedUsers.length} users`,
              timestamp: new Date().toISOString(),
              user: 'Admin',
            }));
            dispatch(setSelectedUsers([]));

            // Refresh the users list
            dispatch(fetchUsers());

            showSuccess(`${selectedUsers.length} users have been deleted successfully!`);
          } catch (error) {
            console.error('Failed to delete users:', error);
            showError(`Failed to delete users: ${error.message || 'Please try again.'}`);
          }
        }
        break;
      default:
        break;
    }
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge class
  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return 'status-badge active';
      case 'inactive':
        return 'status-badge inactive';
      default:
        return 'status-badge';
    }
  };

  // Get role badge class
  const getRoleBadge = (role) => {
    switch (role) {
      case 'buyer':
        return 'role-badge buyer';
      case 'seller':
        return 'role-badge seller';
      case 'admin':
        return 'role-badge admin';
      default:
        return 'role-badge';
    }
  };

  return (
    <AdminLayout>
      <div className="AdminUserManagement">
        {/* Header Actions */}
        <div className="AdminUserManagement__header">
          <div className="header-left">
            <div className="search-container">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search users by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="AdminUserManagement__filters">
          <div className="filter-group">
            <FaFilter className="filter-icon" />
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Roles</option>
              <option value="buyer">Buyers</option>
              <option value="seller">Sellers</option>
              <option value="admin">Admins</option>
            </select>
          </div>

          <div className="filter-group">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          {selectedUsers.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedUsers.length} selected
              </span>
              <button
                className="btn btn-outline"
                onClick={() => handleBulkAction('activate')}
              >
                Activate
              </button>
              <button
                className="btn btn-outline"
                onClick={() => handleBulkAction('deactivate')}
              >
                Deactivate
              </button>
              <button
                className="btn btn-danger"
                onClick={() => handleBulkAction('delete')}
              >
                Delete
              </button>
            </div>
          )}
        </div>

        {/* Users Table */}
        <div className="AdminUserManagement__table">
          <div className="table-container">
            <table className="users-table">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                    />
                  </th>
                  <th>User</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Join Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user?._id || user?.id || Math.random()}>
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user?._id || user?.id)}
                        onChange={() => handleSelectUser(user?._id || user?.id)}
                      />
                    </td>
                    <td>
                      <div className="user-info">
                        <div className="user-avatar">
                          {user?.profileImage ? (
                            <img src={user.profileImage} alt={user?.firstName || 'User'} />
                          ) : (
                            (user?.role || 'buyer') === 'seller' ? <FaUserTie /> : <FaUsers />
                          )}
                        </div>
                        <div className="user-details">
                          <span className="user-name">
                            {user?.firstName || 'Unknown'} {user?.lastName || 'User'}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td>{user?.email || 'No email'}</td>
                    <td>
                      <span className={getRoleBadge(user?.role)}>
                        {user?.role || 'buyer'}
                      </span>
                    </td>
                    <td>
                      <div className="status-toggle">
                        <span className={getStatusBadge(user?.status)}>
                          {user?.status || 'inactive'}
                        </span>
                        <button
                          className="toggle-btn"
                          onClick={() => handleUserAction(user, 'toggle')}
                          title={`${(user?.status || 'inactive') === 'active' ? 'Deactivate' : 'Activate'} user`}
                        >
                          {(user?.status || 'inactive') === 'active' ? (
                            <FaToggleOn className="toggle-on" />
                          ) : (
                            <FaToggleOff className="toggle-off" />
                          )}
                        </button>
                      </div>
                    </td>
                    <td>{formatDate(user?.dateJoined || new Date())}</td>
                    <td>
                      <div className="table-actions">
                        <button
                          className="btn-action view"
                          title="View User"
                          onClick={() => handleUserAction(user, 'view')}
                        >
                          <FaEye />
                        </button>
                        <button
                          className="btn-action edit"
                          title="Edit User"
                          onClick={() => handleUserAction(user, 'edit')}
                        >
                          <FaEdit />
                        </button>
                        <button
                          className="btn-action delete"
                          title="Delete User"
                          onClick={() => handleUserAction(user, 'delete')}
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="no-results">
              <FaUsers className="no-results-icon" />
              <h3>No users found</h3>
              <p>Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>

        {/* Pagination */}
        <div className="AdminUserManagement__pagination">
          <div className="pagination-info">
            Showing {filteredUsers.length} of {users.data?.length || 0} users
          </div>
          <div className="pagination-controls">
            <button className="btn btn-outline" disabled>Previous</button>
            <span className="page-number active">1</span>
            <button className="btn btn-outline" disabled>Next</button>
          </div>
        </div>

        {/* Test Component - Remove this after testing */}
        <UserModalTest />

        {/* Modals */}
        {ui.showUserDetailModal && <UserDetailModal />}
      </div>
    </AdminLayout>
  );
};

export default AdminUserManagement;
