/* UserDetailModal Component Styles */
.UserDetailModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500; /* Above navbar (1000) and sidebar (200) */
  display: flex;
  align-items: center;
  justify-content: center;
}

.UserDetailModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.UserDetailModal__container {
  position: relative;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.UserDetailModal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.header-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--heading4);
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-basic-info h2 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.user-badges {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.status-icon {
  font-size: var(--basefont);
}

.status-icon.active {
  color: #10b981;
}

.status-icon.inactive {
  color: #ef4444;
}

.verification-icon {
  font-size: var(--basefont);
  color: #3b82f6;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background-color: var(--light-gray);
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Content */
.UserDetailModal__content {
  flex: 1;
  padding: var(--heading6);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Sections */
.info-section,
.stats-section,
.activity-section,
.actions-section {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
}

.section-header h3,
.stats-section h3,
.activity-section h3 {
  margin: 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.info-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.info-icon {
  color: var(--btn-color);
  font-size: var(--basefont);
  flex-shrink: 0;
}

.info-item div {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 600;
}

.info-value {
  font-size: var(--smallfont);
  color: var(--text-color);
}

/* Edit Form */
.edit-form {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.form-group label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.form-input,
.form-select {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.form-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: flex-end;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--btn-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--heading6);
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-number {
  font-size: var(--heading5);
  font-weight: 700;
  color: var(--secondary-color);
}

.stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Activity List */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
  max-height: 200px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.activity-content {
  display: flex;
  flex-direction: column;
}

.activity-description {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.activity-date {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.activity-amount {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--btn-color);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--basefont);
  justify-content: center;
}

/* Buttons */
.btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.btn.btn-primary:hover:not(:disabled) {
  background-color: #d32f2f;
}

.btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.btn.btn-outline:hover:not(:disabled) {
  background-color: var(--bg-gray);
}

.btn.btn-success {
  background-color: #10b981;
  color: var(--white);
}

.btn.btn-success:hover:not(:disabled) {
  background-color: #059669;
}

.btn.btn-warning {
  background-color: #f59e0b;
  color: var(--white);
}

.btn.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
}

.btn.btn-danger {
  background-color: #ef4444;
  color: var(--white);
}

.btn.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
}

/* Enhanced form validation styles */
.form-input.error,
.form-select.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-text {
  color: #ef4444;
  font-size: var(--extrasmallfont);
  margin-top: 0.25rem;
  display: block;
}

.error-message.general-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  padding: var(--smallfont);
  margin-bottom: var(--basefont);
  color: #dc2626;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-size: var(--smallfont);
}

/* Loading spinner */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced form labels */
.form-group label {
  font-weight: 600;
  margin-bottom: var(--smallfont);
  display: block;
  color: var(--secondary-color);
}

/* Disabled form elements */
.form-input:disabled,
.form-select:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Enhanced button loading states */
.btn .spinner {
  margin-right: var(--smallfont);
}

/* Delete confirmation modal */
.delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1600; /* Above the main modal */
}

.delete-confirm-modal {
  background: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--heading6);
  max-width: 400px;
  width: 90%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.delete-confirm-header {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-bottom: var(--basefont);
}

.delete-confirm-header .warning-icon {
  color: #f59e0b;
  font-size: var(--heading5);
}

.delete-confirm-header h3 {
  margin: 0;
  color: var(--secondary-color);
  font-size: var(--heading6);
  font-weight: 600;
}

.delete-confirm-content {
  margin-bottom: var(--heading6);
}

.delete-confirm-content p {
  margin: 0 0 var(--smallfont) 0;
  color: var(--text-color);
  line-height: 1.5;
  font-size: var(--smallfont);
}

.delete-confirm-content .warning-text {
  color: #dc2626;
  font-weight: 500;
  font-size: var(--extrasmallfont);
}

.delete-confirm-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: flex-end;
}

.delete-confirm-actions .btn {
  min-width: 120px;
}

/* Enhanced action buttons */
.action-buttons .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-buttons .btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced form actions */
.form-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: flex-end;
  margin-top: var(--heading6);
  padding-top: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

.form-actions .btn {
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
}

/* Responsive styles */
@media (max-width: 768px) {
  .UserDetailModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .UserDetailModal__header {
    padding: var(--basefont);
  }

  .header-info {
    flex-direction: column;
    text-align: center;
  }

  .user-avatar {
    width: 60px;
    height: 60px;
    font-size: var(--heading5);
  }

  .UserDetailModal__content {
    padding: var(--basefont);
  }

  .info-grid,
  .form-row,
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .form-actions {
    justify-content: stretch;
  }

  .form-actions .btn {
    flex: 1;
  }

  .delete-confirm-actions {
    flex-direction: column;
  }

  .delete-confirm-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .UserDetailModal__container {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }
}
